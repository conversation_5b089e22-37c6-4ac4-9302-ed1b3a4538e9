{"version": "1.1", "timestamp": "2025-08-11 10:50:40", "camera_settings": {"resolution_index": 2, "exposure": -5.0, "brightness": -30.0}, "led_settings": {"num_green": 33, "num_red": 2, "rois": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "off_samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "on_samples": [[], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], [], []], "gray_threshold_green": 29.636363636363633, "green_threshold": 38.83075472507291, "gray_threshold_red": 44.5, "red_threshold": 35.767412698412706}, "digit_settings": {"digit_rois": [[268, 19, 107, 149], [379, 13, 104, 166]], "segment_rois": [[[316, 38, 41, 9], [355, 59, 10, 26], [345, 112, 10, 27], [299, 144, 38, 9], [291, 105, 10, 27], [301, 49, 10, 35], [309, 92, 38, 9]], [[419, 39, 38, 10], [457, 61, 9, 26], [444, 115, 11, 25], [399, 146, 40, 8], [393, 107, 9, 28], [402, 54, 9, 32], [408, 96, 39, 9]]], "brightness_threshold": 60.0}, "base_alignment": {"enabled": true, "base_points": [[688, 33], [844, 38]], "original_base_points": [[818, 79], [976, 83]], "template_size": 30, "match_threshold": 0.75, "base_templates_b64": ["iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAIAAAC0Ujn1AAADm0lEQVRIDbXB7U8bBRwH8O/v7vrAipQy6jI6HqQbNWmZRelWHjobAzMwpszNLEXN5gtJ1L/G+IIlzodNYM6xOAJUJmGFzS2aRQjKU0cLIVJIquDgKOVo786j0cTlzpf3+RAZn5MzGcgyFESQJSgYFrIMWQZkgMCyxLJEJAkCIIMYyDL2yWAZiCJAUBBABCIoJBPhHwzAYZ8IiNjHACwMJmQEgKBgWEgSOA5EEEUoWBYcg709KDgOQgYcB0kCwJlMBN0QdEPQDUE3BN0QdEPQDeF/WK0HjfkH/kj8DqDgYAlHRZlMht+MASL+w2wq3BWeQgtBi8vlDgQCu1I2EokkluJtrR32ktKsYndjYmIi+mQKOdXV1SdPvLq2tjYUvg0Vgpam1jan0ymK4vj4+MLMbxdCl4uLyohIwnYsFhsd7kdOffDU8eMv7+zsfHO9e29vB88iaGl7q720tBTgJicnf7o/dvGdD202myAIeQdYnuenpqZ+/eURgEBjs7vmpVQq9fUXXVAhaGl582xZWZnE5M3Pzz+4Oxh672Or1SoIAsNmDQbDxsbGtzdvIps62dDi87q3t7e/unYFKgQtzWdanE6nLJmi0ejYyPC7lz8qsJqFHGPO9PT0j5HhEw2v1dTUbG1t3bj+GVQIWoKnm1wuF2Tz3Nzc/dHwxfc7rVarJEn8X3/abDaCIZ1Oh8Nhl6KqOp1O91z7FCoELS0X2h0OhyQaFxYWHtwdbA99cNheyPP86OhofX293W4HMDs7S0RVVVXpdLr7yytQIWh5/eyZyspKkcxLS0sj/X3tHZeeP1SUTCbv9PQcdr7Y1Bw0mUxPk5sAbPaSVCrV+/knUCFoqa2t9fv9uyy7uLh4b2DgXEen/ZAlmUze6ekBUO33+3y+zJ5MRHl5eTzP917tggpBi8fjaWxszBotsVhsLNx/rqOz2F6wuro6dKsXgMFaHAqFjEajIAhmszmVSvVe7YIKQcsxrzcYDMqyHI/HI4ODrecvOY7YE4lEuO8Gck63v11WXpLNZk0Mt76+3tfbDRWCFo/HEwgEBA7RaPThD/ea3jh/tKI0kUgM3L6FnCOVRxuCpywWCythZWXl+/7voELQ4na76+rqJI6ZVPz82NvQ4PV6Z2ZmHo+N4V/umld8Ph/HccvLyyNDA1AhaMnPz6+oqEiLmfhcFApiHcfKE08W8SzHC+WFhcWbm5srizGoEHRD0A1BNwTdEHRD0M3fVYlxW4Ao4q8AAAAASUVORK5CYII=", "iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAIAAAC0Ujn1AAAElklEQVRIDbXB7U8adwAH8O/vHuDOwyIxDBWhU9i0Dtd2HSptBZTGbO2ypWm7rOtemqbZP9clm2nVgjarG3E+0Vqs9QlFOZAWpCgUuPvd3C0m88VeLAufD8EpBNBwCscwDKUUUAAWYAEKKAALqICBgAWgQQMUQANYgAFqACGoG4K6IagbgrohqBuCuiGoG4ITrS0dmqaVS2qhmAJU/G+kr++y3W4XRZFjBVVVD/KlZDL5fPkZTpx1drndbhBlZWUlldrBP3i9vnK5vLy8JEnmixcvqgo5Ojqq1kqCIBi4JjI6+gA6pQZCiFJjisXiyur86upLAC0t7T6fr7m5OZfbj0ajqVQKJ4LBoMPhSKff5PP5lpYWq9VKCAHAMAylFBpH7t//UVGUbDablt+cOXOmyWISBOHosLK2tvYyPt/W5vT7/SaTKZuVI5FIsViETpKkkZERs9mcSOxSSqWm5oaGBhBFFEWeEzVNoypHRkcfLC0tzc1FAXBi08ClCy6XC0Amk5mOzJpMputfBUVRzGaz4XC4UHgLnc1mCwRCJpNpfz8ly3JsZVVVVYvU6PF42jvcDMOUDyrk9u3vIpFILrcPXXe3x+v1CoKQTqdnfl0URfHLGwGe57PZ7Ozs7M7OJnQOh+PyZb8kSdvb2+HwBKDhGMsHQyFHe4eqqjvrm+TmzTtjk+PVw3fQOVwfhUIhgeGy2Wz09zmj0RgMBo1GYy6/v7i4+Pp1HDqbzdY/eKWxsVHeliORCfyNNwUCAYeztVqtprZS5MaNb8Ymx1F9D527xxMIBBiGKRQKz8LPOI4bHh4WRfFtLrOwsLC+/gq6tra2YDAoCEIm8/bx48eqWsFf2IHBIZf7LKVUTsjk+vWvp6eflEol6Lp6z/t8PpZlC4XC06k/OI77IuQXBCFbyMdisbX4InROp3PAf1UQBFmWJx89AsUxrsHk8/mcTmetVtvb3CO3bt1aWHi+tbUGXWd3j9/vZ1n24OAg/OSpJEmhawGe54vvyrFYbHV5Hjqbwz58dVgUxW15Lzw5CaUKgBjF/v7+zs5ORVFSWyly597d1dWN53Oz0A0MXvN4PCzLZjKZX8Z+stlsI8Fhg8FwUDqcn59fjy9D13q2Y3DQJwhCOv1mYnwctQp0fVf9LpeLUppK7JBvf/g+mZSj0ahWUS54+89//hnP81Aq8Xj8t+mIu8czMDAgCEK5XN7Y2Jifi9VKeYA9f8n/6QU3x3F7e3sTY2PQVOj6+690dXVpmpZMJsmde3drNe0YxwqNjY0spxFCSqXSw4cPy4W869wnLpfLYrHwPF8oFPK5IqXUbDZbrVae5yuVyuSTnzPJPegMhgav1/vhx25K6e7GLhkaGrLYrJIksawRACHk8PBwZmYmldiErr3TfcxqtRqNRp7ncUzjCCHvK4dTU1Py9hZOGAwNvb297p5uSunO2g4xm82uc10WiwW8UdO0RCKx+eIFTrPb7VZ7q9VqlSQzy7KKohwdHUUiE/R9FaeQvj7fB+1tuVxuPf6K4D9hOFAV0PAvmpqaqlVaKr0DQFA3BHVDUDcEdUNQNwR18yfCrgwwtsTVpgAAAABJRU5ErkJggg=="]}, "original_rois": {"led_rois": [[794, 102, 13, 12], [795, 128, 13, 12], [793, 154, 13, 12], [793, 180, 13, 12], [793, 206, 13, 12], [793, 232, 13, 12], [792, 259, 13, 12], [793, 285, 13, 12], [827, 103, 13, 12], [827, 129, 13, 12], [828, 155, 13, 12], [827, 181, 13, 12], [828, 207, 13, 12], [828, 233, 13, 12], [827, 259, 13, 12], [826, 286, 13, 12], [952, 105, 13, 12], [952, 130, 13, 12], [952, 157, 13, 12], [951, 183, 13, 12], [952, 209, 13, 12], [952, 235, 13, 12], [952, 262, 13, 12], [952, 287, 13, 12], [985, 105, 13, 12], [985, 133, 13, 12], [986, 157, 13, 12], [986, 184, 13, 12], [986, 210, 13, 12], [986, 236, 13, 12], [985, 262, 13, 12], [987, 289, 11, 11], [644, 196, 28, 11], [650, 67, 24, 14], [664, 132, 30, 12]], "digit_rois": [[399, 65, 107, 149], [510, 59, 104, 166]], "digit_segment_rois": [[[447, 84, 41, 9], [486, 105, 10, 26], [476, 158, 10, 27], [430, 190, 38, 9], [422, 151, 10, 27], [432, 95, 10, 35], [440, 138, 38, 9]], [[550, 85, 38, 10], [588, 107, 9, 26], [575, 161, 11, 25], [530, 192, 40, 8], [524, 153, 9, 28], [533, 100, 9, 32], [539, 142, 39, 9]]]}, "analysis_settings": {"logging_duration": 50.0}}